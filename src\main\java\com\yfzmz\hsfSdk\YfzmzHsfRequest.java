package com.yfzmz.hsfSdk;

import cn.hutool.http.Method;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p> 请求参数 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 14:18
 **/
public interface YfzmzHsfRequest<T extends YfzmzHsfResponse> {

    /**
     * 获取API路径
     *
     * 此方法用于返回当前配置或上下文中定义的API路径信息
     * 它不接受任何参数，也不返回任何特定类型的值
     */
    String getApiPath();

    /**
     * 获取令牌
     */
    String getToken();

    /**
     * 获取请求方法
     *
     * @return 请求方法
     */
    Method getMethod();

    /**
     * 获取请求参数的类型
     *
     * 此方法用于确定请求参数的类型，以便在处理请求时能够正确解析和处理参数
     * 返回值是一个字符串，表示请求参数的类型
     *
     * @return 请求参数的类型字符串
     */
    HsfRequestParamType getRequestParamType();

    /**
     * 得到当前API的响应结果类型
     *
     * @return 响应类型
     */
    Class<T> getResponseClass();

    /**
     * 判断是否需要加密
     */
    boolean isNeedEncrypt();

    /**
     * 设置请求是否需要加密
     * @param needEncrypt 是否需要加密
     */
    void setNeedEncrypt(boolean needEncrypt);

    /**
     * 获取业务模型对象
     *
     * @return YfzmzObject 返回当前的业务模型实例，用于进一步处理或访问业务数据
     */
    YfzmzHsfObject getBizModel();

    /**
     * 设置业务模型对象
     *
     * @param bizModel YfzmzObject类型 参数bizModel是新的业务模型实例，用于更新当前对象的业务数据
     */
    void setBizModel(YfzmzHsfObject bizModel);


    /**
     * 获取文件
     */
    MultipartFile getMultipartFile();
}
