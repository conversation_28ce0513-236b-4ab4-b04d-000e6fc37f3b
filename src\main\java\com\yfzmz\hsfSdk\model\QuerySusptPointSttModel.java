package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 监管端违规统计
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuerySusptPointSttModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 统计时间开始
     */
    private String sttTimeStart;

    /**
     * 统计时间结束
     */
    private String sttTimeEnd;

    /**
     * 医保区划
     */
    private String admdvs;
}



