package com.yfzmz.hsfSdk.request;

import cn.hutool.http.Method;
import com.yfzmz.hsfSdk.HsfRequestParamType;
import com.yfzmz.hsfSdk.YfzmzHsfObject;
import com.yfzmz.hsfSdk.YfzmzHsfRequest;
import com.yfzmz.hsfSdk.response.login.LoginHsfResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p> 登录请求参数 </p>
 *
 * 该类用于封装登录请求的相关参数，实现YfzmzRequest接口以支持登录功能
 * 主要包含token获取、请求方法、请求参数类型、是否需要加密等方法
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginHsfRequest implements YfzmzHsfRequest<LoginHsfResponse> {

    // 登录令牌
    private String token;
    // 是否需要加密，默认为false
    private boolean needEncrypt = true;
    // 业务模型对象，用于封装业务相关数据
    private YfzmzHsfObject bizModel = null;

    /**
     * 获取API路径
     *
     * @return API路径，用于指定登录的接口路径
     */
    @Override
    public String getApiPath() {
        return "/authorisation/oauth/token";
    }

    /**
     * 获取请求方法
     *
     * @return 请求方法，此处为POST，用于指定登录请求的HTTP方法
     */
    @Override
    public Method getMethod() {
        return Method.POST;
    }

    /**
     * 获取请求参数类型
     *
     * @return 请求参数类型，此处为FORM，用于指定登录请求参数的提交类型
     */
    @Override
    public HsfRequestParamType getRequestParamType() {
        return HsfRequestParamType.FORM;
    }

    /**
     * 获取token
     *
     * @return 空字符串，此处不实现token的获取逻辑
     */
    @Override
    public String getToken() {
        return "";
    }

    /**
     * 获取响应类
     *
     * @return LoginResponse类，用于指定登录成功后的响应数据类型
     */
    @Override
    public Class<LoginHsfResponse> getResponseClass() {
        return LoginHsfResponse.class;
    }

    /**
     * 是否需要加密
     *
     * @return 需要加密的标志，决定是否对请求数据进行加密
     */
    @Override
    public boolean isNeedEncrypt() {
        return this.needEncrypt;
    }

    /**
     * 设置是否需要加密
     *
     * @param needEncrypt 需要加密的标志，用于设置请求数据是否需要加密
     */
    @Override
    public void setNeedEncrypt(boolean needEncrypt) {
        this.needEncrypt = needEncrypt;
    }

    /**
     * 获取业务模型
     *
     * @return YfzmzObject对象，用于获取封装业务相关数据的对象
     */
    @Override
    public YfzmzHsfObject getBizModel() {
        return this.bizModel;
    }

    /**
     * 设置业务模型
     *
     * @param bizModel YfzmzObject对象，用于设置封装业务相关数据的对象
     */
    @Override
    public void setBizModel(YfzmzHsfObject bizModel) {
        this.bizModel = bizModel;
    }

    /**
     * 获取multipart文件
     *
     * @return null，此处不处理文件上传逻辑
     */
    @Override
    public MultipartFile getMultipartFile() {
        return null;
    }
}
