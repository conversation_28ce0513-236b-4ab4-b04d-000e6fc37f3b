package com.yfzmz.hsfSdk.response.yb;

import com.alibaba.fastjson.JSONObject;
import com.yfzmz.hsfSdk.response.DataHsfResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医保接口出参
 * <AUTHOR>
 * @date 2025-07-19 09:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class YbHsfResponse extends DataHsfResponse<YbHsfResponse> {

    private static final long serialVersionUID = -230181036026851444L;

    /**
     * 出参编码
     */
    private String code;

    /**
     * 类型
     */
    private String type;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 返回结果
     */
    private JSONObject data;
}
