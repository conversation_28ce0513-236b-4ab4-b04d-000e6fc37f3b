package com.yfzmz.hsfSdk.response.login;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TokenResponse 类用于封装与认证相关的响应信息
 * 它主要包含访问令牌、刷新令牌、令牌过期时间等属性，
 * 用于表示认证成功后的响应数据
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:17
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokenResponse implements Serializable {

    private static final long serialVersionUID = 1471724724403410619L;
    /**
     * accessToken 是用户访问资源所需的令牌
     */
    private String accessToken;

    /**
     * refreshToken 用于在accessToken过期后获取新的accessToken
     * 它允许用户在不重新提供认证信息的情况下继续访问受保护的资源
     */
    private String refreshToken;

    /**
     * expiresIn 表示accessToken的有效期，单位为秒
     * 它告知客户端令牌的过期时间，以便在令牌过期前采取相应措施
     */
    private long expiresIn;

    /**
     * scope 描述了accessToken所拥有的权限范围
     * 它限制了令牌可以访问的资源和执行的操作
     */
    private String scope;

    /**
     * jti (JWT ID) 是一个唯一的标识符，用于标识令牌
     * 它有助于跟踪和撤销令牌
     */
    private String jti;

    /**
     * type 描述了令牌的类型
     * 通常用于指示令牌的格式或用途
     */
    private String type;
}

