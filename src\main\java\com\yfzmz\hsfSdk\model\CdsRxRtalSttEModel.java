package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 药店信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CdsRxRtalSttEModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 数据唯一记录号集合
     */
    private List<String> rids;

    /**
     * 编码集合
     */
    private String[] rtalPhacCodeList;

    /**
     * 处方零售药店信息统计ID
     */
    private String rxRtalSttId;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 医保区划名称
     */
    private String admdvsName;

    /**
     * 零售药店编码
     */
    private String fixmedinsCode;

    /**
     * 零售药店编码集合
     */
    private String[] medinsCodeList;

    /**
     * 零售药店名称
     */
    private String fixmedinsName;

    /**
     * 统计日期
     */
    private String sttDate;

    /**
     * 处方结算数量
     */
    private Integer rxSetlCnt;

    /**
     * 处方结算金额
     */
    private BigDecimal rxSetlAmt;

    /**
     * 处方结算人数
     */
    private Integer rxSetlPsnCnt;

    /**
     * 处方结算药品数量
     */
    private Integer rxSetlDrugCnt;

    /**
     * 处方统筹基金支付金额
     */
    private BigDecimal rxHifiPay;

    /**
     * 处方个人账户支付金额
     */
    private BigDecimal rxAcctPay;

    /**
     * 处方现金支付金额
     */
    private BigDecimal rxCashpay;

    /**
     * 统筹区编号
     */
    private String poolareaNo;

    /**
     * 统筹区名称
     */
    private String poolareaName;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 开始时间
     */
    private String sttDateStart;

    /**
     * 结束时间
     */
    private String sttDateEnd;

}



