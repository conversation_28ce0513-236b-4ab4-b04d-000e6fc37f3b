package com.yfzmz.hsfSdk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 购药明细入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrugDetailModel {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 费用明细流水号
     */
    private String feedetlSn;

    /**
     * 处方号
     */
    private String rxno;

    /**
     * 外购处方标志
     */
    private String rxCircFlag;

    /**
     * 费用发生时间
     */
    private String feeOcurTime;

    /**
     * 医疗目录编码
     */
    private String medListCodg;

    /**
     * 医药机构目录编码
     */
    private String medinsListCodg;

    /**
     * 明细项目费用总额
     */
    private BigDecimal detItemFeeSumamt;

    /**
     * 数量
     */
    private BigDecimal cnt;

    /**
     * 单价
     */
    private BigDecimal pric;
}
