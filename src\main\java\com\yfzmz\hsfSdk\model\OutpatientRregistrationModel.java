package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

/**
 * 挂号入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientRregistrationModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 住院/门诊号
     */
    private String iptOtpNo;
    /**
     * 医师编码
     */
    private String atddrNo;
    /**
     * 医师姓名
     */
    private String drName;
    /**
     * 科室编码
     */
    private String deptCode;
    /**
     * 科室名称
     */
    private String deptName;
    /**
     * 科别
     */
    private String caty;
}
