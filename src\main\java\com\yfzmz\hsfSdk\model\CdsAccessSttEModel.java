package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 定点医疗机构统计信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CdsAccessSttEModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 机构编码
     */
    private String fixmedinsCode;

    /**
     * 机构名称
     */
    private String fixmedinsName;

    /**
     * 机构名称
     */
    private String medinsName;

    /**
     * 药店名称
     */
    private String rtalPhacName;

    private String medinsType;

    private String medinslv;

    private String medinsNatu;

}



