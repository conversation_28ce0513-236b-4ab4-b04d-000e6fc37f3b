package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 患者信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CdsRxPatnSttEModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 年龄类型
     */
    private String ageType;

    /**
     * 性别
     */
    private String gend;


    /**
     * 开始时间
     */
    private String sttDateStart;

    /**
     * 结束时间
     */
    private String sttDateEnd;
}



