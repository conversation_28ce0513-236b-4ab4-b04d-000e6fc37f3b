package com.yfzmz.hsfSdk;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.yfzmz.hsfSdk.util.FastJsonUtils;
import com.yfzmz.hsfSdk.util.sm4.SM4Utils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

/**
 * <p>抽象客户端类，实现YfzmzClient接口，提供基础的请求执行功能</p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 14:51
 * @since 1.0.0
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbstractYfzmzHsfClient implements YfzmzHsfClient {

    /**
     * 网关服务地址，默认指向测试环境
     */
    private String serverUrl = "https://test.yfzmz01.com/gateway";

    /**
     * 加密算法常量
     */
    public static final String ENCRYPT_CODE = "encType";
    public static final String TIMESTAMP = "timestamp";
    public static final String CONTENT = "encData";
    public static final String PRIVATE_KEY = "N2MwYTg4MWNhMDk0NjllNTUzNDA4ZGI4Y2YwYmI5NTQ=";

    /**
     * 执行易复诊铭智请求的通用方法
     * 该方法是一个泛型方法，允许传入任何继承了YfzmzResponse类型的响应对象
     * 主要用于简化请求的执行过程，将请求的发送和响应的处理封装在一起
     *
     * @param request 易复诊铭智请求对象，包含了请求的所有参数和信息
     * @param <T>     泛型参数，代表返回的响应对象类型，必须是YfzmzResponse的子类
     * @return 返回处理后的响应对象，类型为泛型参数T指定的类型
     */
    @Override
    public <T extends YfzmzHsfResponse> T execute(YfzmzHsfRequest<T> request) {
        return execute(request, request.getToken());
    }

    /**
     * 使用指定的token执行易复诊铭智请求的通用方法
     * 该方法是一个泛型方法，允许传入任何继承了YfzmzResponse类型的响应对象
     * 主要用于简化请求的执行过程，特别是当需要使用特定的认证令牌时
     *
     * @param request 易复诊铭智请求对象，包含了请求的所有参数和信息
     * @param token   字符串类型的认证令牌，用于请求的认证
     * @param <T>     泛型参数，代表返回的响应对象类型，必须是YfzmzResponse的子类
     * @return 返回处理后的响应对象，类型为泛型参数T指定的类型
     */
    @Override
    public <T extends YfzmzHsfResponse> T execute(YfzmzHsfRequest<T> request, String token) {
        log.info("serverUrl -> {}", serverUrl);
        HttpRequest post = HttpRequest.of(serverUrl + request.getApiPath()).method(request.getMethod());
        if (!StringUtils.isEmpty(token)) {
            post.auth(token);
        }

        String contentType = "application/json";
        //获取请求参数
        YfzmzHsfObject bizModel = request.getBizModel();
        String jsonString = JSONObject.toJSONString(bizModel);
        switch (request.getRequestParamType()) {
            case JSON:
                if (request.isNeedEncrypt()) {
                    jsonString = encrypt(jsonString);
                }
                post.body(jsonString);
                break;
            case FORM:
                contentType = "application/x-www-form-urlencoded";
                JSONObject jsonForm = JSONObject.parseObject(jsonString);
                post.form(jsonForm.getInnerMap());
                break;
            case FILE:
                contentType = "application/form-data";
                JSONObject jsonFile = JSONObject.parseObject(jsonString);
                post.form(jsonFile.getInnerMap());
                if (!ObjectUtils.isEmpty(request.getMultipartFile())) {
                    post.form("multipartFile", getInputStream(request.getMultipartFile()));
                }
                break;
            default:
                break;
        }
        post.header("Content-Type", contentType);
        String body = post.execute().body();
        log.info("请求结果 -> {}", body);
        return fromJson(body, request.getResponseClass());
    }


    /**
     * 获取文件输入流
     *
     * @param file 文件对象
     * @return 文件输入流
     * @throws IOException 如果转换文件输入流时出现异常
     */
    public static InputStreamResource getInputStream(MultipartFile file) {
        try {
            return new InputStreamResource(file.getInputStream(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("文件流转换异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 将 JSON 字符串转换为 YfzmzResponse 对象
     *
     * @param json  JSON 字符串
     * @param clazz 泛型数据的类类型
     * @return 返回解析后的 YfzmzResponse 对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("jsonObject -> {}", jsonObject);
        int code = jsonObject.getIntValue("code");
        if (code == 0) {
            jsonObject = decrypt(jsonObject.getJSONObject("data"));
        }
        log.info("jsonObject -> {}", jsonObject);
        return FastJsonUtils.objectToModel(jsonObject, clazz);
    }

    /**
     * 加密请求数据
     *
     * @param body 需要加密的原始字符串数据
     * @return 加密后的JSON格式字符串，包含加密元数据和加密内容
     */
    public static String encrypt(String body) {
        //请求时间戳
        String timestamp = System.currentTimeMillis() + "";
        //加密方式
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ENCRYPT_CODE, "SM4");
        jsonObject.put(TIMESTAMP, timestamp);
        //执行SM4对称加密
        jsonObject.put(CONTENT, SM4Utils.encryptDataECB(body, getNewKey(timestamp)));
        return jsonObject.toJSONString();
    }

    /**
     * 解密请求结果
     *
     * @param jsonObject 包含加密数据的JSON对象，需包含以下字段：
     *                   - dataExchangeId: 数据交换ID
     *                   - encryptType: 加密类型
     *                   - timestamp: 时间戳
     *                   - content: 加密内容
     * @return 解密后的JSON对象
     */
    public static JSONObject decrypt(JSONObject jsonObject) {
        log.info("请求结果解密开始");
        String timestamp = jsonObject.getString(TIMESTAMP);
        String content = jsonObject.getString(CONTENT);
        /*
         * 组装密钥并解密
         * 1. 使用dataExchangeId和timestamp生成密钥
         * 2. 通过SM4算法ECB模式解密内容
         * 3. 将解密后的内容解析为新的JSON对象
         */
        String str = SM4Utils.decryptDataECB(content, getNewKey(timestamp));
        jsonObject = JSONObject.parseObject(str);
        log.info("请求结果解密结束");
        return jsonObject;
    }

    /**
     * 获取新的加密key
     * @param timestamp
     * @return
     */
    public static String getNewKey(String timestamp){
        String finalKey = Base64.getEncoder().encodeToString((PRIVATE_KEY + timestamp).getBytes(StandardCharsets.UTF_8));
        return finalKey.substring(0, 32);
    }
}
