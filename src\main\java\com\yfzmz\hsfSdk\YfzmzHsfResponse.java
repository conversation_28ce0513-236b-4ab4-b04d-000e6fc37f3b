package com.yfzmz.hsfSdk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <p> 响应结果 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 14:18
 **/
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class YfzmzHsfResponse implements Serializable {
    private static final long serialVersionUID = -6537213027001504967L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 业务数据
     */
//    private Object data;
//
//    /**
//     * 数据交换流水号（yyyyMMddHHmmssSSS + 6位随机数）
//     */
//    private String dataExchangeId;
//
//    /**
//     * 加密方式
//     */
//    private String encryptType;
//
//    /**
//     * 时间戳（yyyyMMddHHmmssSSS）
//     */
//    private String timestamp;
//
//    /**
//     * 请求数据
//     */
//    private String content;
}
