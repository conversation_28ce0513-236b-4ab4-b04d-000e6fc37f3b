package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 特病备案入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddEvtInfoModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 开始日期
     */
    private Date beginDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 申报来源
     */
    private String dclaSouc;

    /**
     * 医保目录列表
     */
    private List<MedInSuDirListModel> medInsuDirList;

    /**
     * 定点医疗机构列表
     */
    private List<PsnFixedEvtDetlDtosModel> psnFixedEvtDetldtos;

}
