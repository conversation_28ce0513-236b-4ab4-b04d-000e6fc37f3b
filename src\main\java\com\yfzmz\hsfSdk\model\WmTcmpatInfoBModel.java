package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 西药中成药信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmTcmpatInfoBModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 医疗目录编码
     */
    private String medListCodg;

    /**
     * 医疗目录编码集合
     */
    private List<String> medListCodgs;
    /**
     * 药品商品名
     */
    private String drugProdname;

    /**
     * 通用名编码
     */
    private String gennameCodg;

    /**
     * 药品通用名
     */
    private String drugGenname;

    /**
     * 化学名称
     */
    private String chemname;

    /**
     * 别名
     */
    private String alis;

    /**
     * 英文名称
     */
    private String engName;

    /**
     * 注册名称
     */
    private String regName;

    /**
     * 药品本位码
     */
    private String drugstdcode;

    /**
     * 药品剂型
     */
    private String drugDosform;

    /**
     * 药品剂型名称
     */
    private String drugDosformName;

    /**
     * 药品类别
     */
    private String drugType;

    /**
     * 药品类别名称
     */
    private String drugTypeName;

    /**
     * 药品规格
     */
    private String drugSpec;

    /**
     * 药品规格代码
     */
    private String drugSpecCode;

    /**
     * 注册剂型
     */
    private String regDosform;

    /**
     * 注册规格
     */
    private String regSpec;

    /**
     * 注册规格代码
     */
    private String regSpecCode;

    /**
     * 每次用量
     */
    private String eachDos;

    /**
     * 使用频次
     */
    private String usedFrqu;

    /**
     * 酸根盐基
     */
    private String acdbas;

    /**
     * 国家药品编号
     */
    private String natDrugNo;

    /**
     * 使用方法
     */
    private String usedMtd;

    /**
     * 中成药标志
     */
    private String tcmpatFlag;

    /**
     * 生产地类别
     */
    private String prodplacType;

    /**
     * 生产地类别名称
     */
    private String prodplacTypeName;

    /**
     * 计价单位类型
     */
    private String prcuntType;

    /**
     * 非处方药标志
     */
    private String otcFlag;

    /**
     * 非处方药标志名称
     */
    private String otcFlagName;

    /**
     * 包装材质
     */
    private String pacmatl;

    /**
     * 包装材质名称
     */
    private String pacmatlName;

    /**
     * 包装规格
     */
    private String pacspec;

    /**
     * 包装数量
     */
    private String pacCnt;

    /**
     * 功能主治
     */
    private String efccAtd;

    /**
     * 给药途径
     */
    private String rute;

    /**
     * 说明书
     */
    private String manl;

    /**
     * 开始日期
     */
    private Date begndate;

    /**
     * 开始日期集合
     */
    private List<String> begndateList;

    /**
     * 结束日期
     */
    private Date enddate;

    /**
     * 结束日期集合
     */
    private List<String> enddateList;

    /**
     * 最小使用单位
     */
    private String minUseunt;

    /**
     * 最小销售单位
     */
    private String minSalunt;

    /**
     * 最小计量单位
     */
    private String minUnt;

    /**
     * 最小包装数量
     */
    private Integer minPacCnt;

    /**
     * 最小包装单位
     */
    private String minPacunt;

    /**
     * 最小制剂单位
     */
    private String minPrepunt;

    private String minpacuntName;

    /**
     * 最小制剂单位名称
     */
    private String minPrepuntName;

    /**
     * 转换比
     */
    private Integer convrat;

    /**
     * 药品有效期
     */
    private String drugExpy;

    /**
     * 最小计价单位
     */
    private String minPrcunt;

    /**
     * 五笔助记码
     */
    private String wubi;

    /**
     * 拼音助记码
     */
    private String pinyin;

    /**
     * 分包装厂家
     */
    private String subpckFcty;

    /**
     * 生产企业代码
     */
    private String prodentpCode;

    /**
     * 生产企业名称
     */
    private String prodentpName;

    /**
     * 特殊限价药品标志
     */
    private String spLmtpricDrugFlag;

    /**
     * 特殊药品标志
     */
    private String spDrugFlag;

    /**
     * 限制使用范围
     */
    private String lmtUsescp;

    /**
     * 限制使用标志
     */
    private String lmtUsedFlag;

    /**
     * 药品注册证编号
     */
    private String drugRegno;

    /**
     * 药品注册证号开始日期
     */
    private Date drugRegcertBegndate;

    /**
     * 药品注册证号开始日期集合
     */
    private List<String> drugRegcertBegndateList;

    /**
     * 药品注册证号结束日期
     */
    private Date drugRegcertEnddate;

    /**
     * 药品注册证号结束日期集合
     */
    private List<String> drugRegcertEnddateList;

    /**
     * 批准文号
     */
    private String aprvno;

    /**
     * 批准文号开始日期
     */
    private Date aprvnoBegndate;

    /**
     * 批准文号开始日期集合
     */
    private List<String> aprvnoBegndateList;

    /**
     * 批准文号结束日期
     */
    private Date aprvnoEnddate;

    /**
     * 批准文号结束日期集合
     */
    private List<String> aprvnoEnddateList;

    /**
     * 市场状态
     */
    private String mktStas;

    /**
     * 市场状态名称
     */
    private String mktStasName;

    /**
     * 药品注册批件电子档案
     */
    private String drugRegAppvletrElecacs;

    /**
     * 药品补充申请批件电子档案
     */
    private String splmAppyAppvletrFile;

    /**
     * 国家医保药品目录备注
     */
    private String natHiDruglistMemo;

    /**
     * 基本药物标志名称
     */
    private String basMednFlagName;

    /**
     * 基本药物标志
     */
    private String basMednFlag;

    /**
     * 增值税调整药品标志
     */
    private String advaltaxAdjmDrugFlag;

    /**
     * 增值税调整药品名称
     */
    private String advaltaxAdjmDrugName;

    /**
     * 上市药品目录集药品
     */
    private String lstdDruglistDrug;

    /**
     * 医保谈判药品标志
     */
    private String hiNegoDrugFlag;

    /**
     * 医保谈判药品名称
     */
    private String hiNegoDrugName;

    /**
     * 卫健委药品编码
     */
    private String nhcDrugCodg;

    /**
     * 备注
     */
    private String memo;

    /**
     * 有效标志
     */
    private String valiFlag;

    /**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 创建人ID
     */
    private String crterId;

    /**
     * 创建人姓名
     */
    private String crterName;

    /**
     * 创建机构编号
     */
    private String crteOptinsNo;

    /**
     * 经办人ID
     */
    private String opterId;

    /**
     * 经办人姓名
     */
    private String opterName;

    /**
     * 经办时间
     */
    private Date optTime;

    /**
     * 经办时间集合
     */
    private List<String> optTimeList;

    /**
     * 经办机构编号
     */
    private String optinsNo;

    /**
     * 版本号
     */
    private String ver;

    /**
     * 版本名称
     */
    private String verName;

    /**
     * 儿童用药
     */
    private String chldMedc;

    /**
     * 公司名称
     */
    private String coName;

    /**
     * 仿制药一致性评价药品
     */
    private String consevalDrug;

    /**
     * 经销企业
     */
    private String dstr;

    /**
     * 经销企业联系人
     */
    private String dstrConer;

    /**
     * 经销企业授权书电子档案
     */
    private String dstrAuthFileElecacs;

    /**
     * 国家医保药品目录剂型
     */
    private String natHiDruglistDosform;

    /**
     * 国家医保药品目录甲乙类标识
     */
    private String natHiDruglistChrgitmLv;

    /**
     * 上市许可证持有人
     */
    private String lstdLicHolder;

    /**
     * 下发标志
     */
    private String isuFlag;

    /**
     * 传输数据ID
     */
    private String tramDataId;

    /**
     * 生效时间
     */
    private Date efftTime;

    /**
     * 生效时间集合
     */
    private List<String> efftTimeList;

    /**
     * 失效时间
     */
    private Date invdTime;

    /**
     * 失效时间集合
     */
    private List<String> invdTimeList;

    /**
     * 子版本名称
     */
    private String smlVerName;

    /**
     * 医保支付标准
     */
    private BigDecimal hiPayStd;

    /**
     * 国家采集标识
     */
    private String natClctFlag;

    /**
     * 原药品编码
     */
    private String initDrugCodg;

}



