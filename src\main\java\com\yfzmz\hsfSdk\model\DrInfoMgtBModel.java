package com.yfzmz.hsfSdk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 医师信息管理表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrInfoMgtBModel {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 医师ID
     */
    private String drInfoId;

    /**
     * 医师ID集合
     */
    private List<String> drInfoIds;
    /**
     * 医师代码
     */
    private String drCode;

    /**
     * 医师名字
     */
    private String drName;

    /**
     * 医师专业技术职务(1-医士 2-医师/住院医师 3-主治医师 4-副主任医师 5-主任医师 6-无 9-其他)
     */
    private String drProTechDuty;

    /**
     * 医师专业技术职务名称
     */
    private String drProTechDutyName;

    /**
     * 医疗机构代码
     */
    private String medinsCode;

    /**
     * 医疗机构名称
     */
    private String medinsName;

    /**
     * 执业机构名称
     */
    private String pracinsName;

    /**
     * 执业机构地址
     */
    private String pracinsAddr;

    /**
     * 多点执业标志(0-否 1-是)
     */
    private String mulPracFlag;

    /**
     * 执业地区
     */
    private String pracRegn;

    /**
     * 执业地区名称
     */
    private String pracRegnName;

    /**
     * 医师执业证件号
     */
    private String drPracCertNo;

    /**
     * 医师执业类别 (1-临床 2-中医 3-口腔 4-公共卫生)
     */
    private String drPracType;

    /**
     * 医师执业类别名称
     */
    private String drPracTypeName;

    /**
     * 医师执业范围
     */
    private String drPracScp;

    /**
     * 医师执业范围名称
     */
    private String drPracScpName;

    /**
     * 开通状态编码(0-待审核，1-已生效，2-已失效，3-驳回)
     */
    private String openStasCodg;

    /**
     * 开通状态名称
     */
    private String openStasName;

    /**
     * 离职时间
     */
    private Date nempTime;

    /**
     * 离职时间集合
     */
    private List<String> nempTimeList;

    /**
     * 个人能力简介
     */
    private String psnItro;

    /**
     * 是否双通道标识(0-否,1-是)
     */
    private String dualchnlFlag;

    /**
     * 门诊统筹标志
     */
    private String oppoolFlag;

    /**
     * 特殊病种权限分类（1-认定、2-治疗、3-认定、治疗）
     */
    private String spdisePermType;

    /**
     * 特殊病种权限分类名称
     */
    private String spdisePermName;

    /**
     * 职称
     */
    private String profttl;

    /**
     * 开始日期
     */
    private Date begndate;

    /**
     * 开始日期集合
     */
    private List<String> begndateList;

    /**
     * 结束日期
     */
    private Date enddate;

    /**
     * 结束日期集合
     */
    private List<String> enddateList;

    /**
     * 备注
     */
    private String memo;

    /**
     * 删除标识（0未删除，1已删除）
     */
    private String delFlag;

    /**
     * 统筹区编号
     */
    private String poolareaNo;

    /**
     * 统筹区名称
     */
    private String poolareaName;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 认证编码
     */
    private String crtfCode;

    /**
     * 人员编号
     */
    private String psnNo;

    /**
     * 经办时间
     */
    private Date optTime;

    /**
     * 经办时间集合
     */
    private List<String> optTimeList;

    /**
     * 经办机构编号
     */
    private String optinsNo;

    /**
     * 执业状态：1正常，2暂停，3终止
     */
    private String pracStas;

    /**
     * 执业状态名称
     */
    private String pracStasName;

    /**
     * 有效标志位
     */
    private String valiFlag;

    /**
     * 创建机构编号
     */
    private String crteOptinsNo;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 创建人ID
     */
    private String crterId;

    /**
     * 创建人姓名
     */
    private String crterName;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 经办人
     */
    private String optId;

    /**
     * 经办人姓名
     */
    private String opterName;

}



