package com.yfzmz.hsfSdk.model;

import lombok.*;

import java.util.Date;

/**
 * 诊断信息入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiseinfoModel {

    private static final long serialVersionUID = 2239184773176755963L;


    /**
     * 诊断类别
     */
    private String diagType;

    /**
     * 诊断排序号
     */
    private String diagSrtNo;

    /**
     * 诊断代码
     */
    private String diagCode;

    /**
     * 诊断名称
     */
    private String diagName;

    /**
     * 诊断科室
     */
    private String diagDept;

    /**
     * 诊断医生编码
     */
    private String diseDorNo;

    /**
     * 诊断医生姓名
     */
    private String diseDorName;

    /**
     * 诊断时间
     */
    private Date diagTime;

    /**
     * 有效标志
     */
    private String valiFlag;
}
