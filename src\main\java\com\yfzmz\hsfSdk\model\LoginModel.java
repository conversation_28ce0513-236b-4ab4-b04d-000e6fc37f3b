package com.yfzmz.hsfSdk.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

/**
 * <p> 登录参数 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginModel extends YfzmzHsfObject {
    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 客户端ID，用于标识调用API的客户端
     */
    @JSONField(name = "client_id")
    private String clientId;

    /**
     * 客户端密钥，用于验证客户端身份
     */
    @JSONField(name = "client_secret")
    private String clientSecret;

    /**
     * 授权类型，指示获取令牌所使用的授权模式
     */
    @Builder.Default
    @JSONField(name = "grant_type")
    private String grantType = "client_credentials";
}
