package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 监管端违规明细列表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SusptPointDetailListQueryPageModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 开方时间开始
     */
    @ApiModelProperty(value ="开方时间开始")
    private String prscTimeStart;

    /**
     * 开方时间结束
     */
    @ApiModelProperty(value ="开方时间结束")
    private String prscTimeEnd;

    /**
     * 医保区划
     */
    @ApiModelProperty(value ="医保区划")
    private String admdvs;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 规则类别
     */
    @ApiModelProperty(value = "规则类别")
    private String ruleType;

    /**
     * 定点医疗机构编码
     */
    @ApiModelProperty(value = "定点医疗机构编码")
    private String[] medinsCodeList;

    /**
     * 定点医疗机构名称
     */
    @ApiModelProperty(value = "定点医疗机构名称")
    private String medinsName;

    /**
     * 定点零售药店编码
     */
    @ApiModelProperty(value = "定点零售药店编码")
    private String[] rtalPhacCodeList;

    /**
     * 定点零售药店名称
     */
    @ApiModelProperty(value = "定点零售药店名称")
    private String fixmedinsName;

    /**
     * 医疗类别
     */
    @ApiModelProperty(value = "医疗类别")
    private String medType;

    /**
     * 险种类型
     */
    @ApiModelProperty(value = "险种类型")
    private String insutype;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String patnName;

    /**
     * 检出时间开始
     */
    @ApiModelProperty(value ="检出时间开始")
    private String checkoutTimeStart;

    /**
     * 检出时间结束
     */
    @ApiModelProperty(value ="检出时间结束")
    private String checkoutTimeEnd;

    /**
     * 用户ID
     */
    @ApiModelProperty(value ="用户ID")
    private String userId;

    /**
     * 处方号
     */
    @ApiModelProperty(value ="处方号")
    private String hiRxno;



