package com.yfzmz.hsfSdk;

/**
 * 自定义运行时异常类 YfzmzException
 * 继承自 RuntimeException，用于在程序运行过程中处理特定的异常情况
 * 使用此异常类可以在特定情况下抛出异常，以便调用者可以捕获并进行相应的处理
 * <AUTHOR>
 */
public class YfzmzHsfException extends RuntimeException {
    private static final long serialVersionUID = 3815708439012380119L;

    /**
     * 构造函数，接收一个字符串参数作为异常消息
     *
     * @param message 异常消息，描述发生异常的原因或情况
     */
    public YfzmzHsfException(String message) {
        super(message);
    }
}

