package com.yfzmz.hsfSdk;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>  </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-29 22:08
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DefaultYfzmzHsfClient extends AbstractYfzmzHsfClient {

    /**
     * 客户端ID，用于标识调用方身份
     */
    private String clientId;
    /**
     * 客户端密钥，用于接口签名验证
     */
    private String clientSecret;

    public DefaultYfzmzHsfClient() {
    }

    public DefaultYfzmzHsfClient(YfzmzHsfConfig yfzmzHsfConfig) {
        super(yfzmzHsfConfig.getServerUrl());
        this.clientId = yfzmzHsfConfig.getClientId();
        this.clientSecret = yfzmzHsfConfig.getClientSecret();
    }

    public DefaultYfzmzHsfClient(String serverUrl) {
        super(serverUrl);
    }

    public DefaultYfzmzHsfClient(String serverUrl, String clientId, String clientSecret) {
        super(serverUrl);
        this.clientId = clientId;
        this.clientSecret = clientSecret;
    }
}
