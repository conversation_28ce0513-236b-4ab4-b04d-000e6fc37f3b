package com.yfzmz.hsfSdk.response.login;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p> 用户信息 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoResponse implements Serializable {
    private static final long serialVersionUID = -5334289452128928390L;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 账号
     */
    private String userName;

    /**
     * 用户名
     */
    private String name;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 医院id
     */
    private String hospId;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 药店类型(0:药店门店,1:药店公司)
     * 客户端类型（0：微信app；1：微信小程序；2：微信h5；3：支付宝app；4：支付宝小程序；5：支付宝h5）
     */
    private String type;

    /**
     * 部署方式
     */
    private String deploymentType;

    /**
     * 部署地区
     */
    private String deploymentArea;

    /**
     * 联系电话
     */
    private String mobile;
}
