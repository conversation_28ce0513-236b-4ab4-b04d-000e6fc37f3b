package com.yfzmz.hsfSdk.model;


import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 药品目录信息管理表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DruglistInfoMgtBModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 药品目录ID
     */
    private String druglistId;

    /**
     * 药品目录ID集合
     */
    private List<String> druglistIds;
    /**
     * 非处方药标志
     */
    private String otcFlag;

    /**
     * 非处方标志位名称
     */
    private String otcFlagName;

    /**
     * 中成药标志
     */
    private String tcmpatFlag;

    /**
     * 有效标志位
     */
    private String valiFlag;

    /**
     * 通用名
     */
    private String genname;

    /**
     * 商品名
     */
    private String prodname;

    /**
     * 目录类别 101：西药中成药 102：中药饮片 103：自制剂 104：民族药
     */
    private String listType;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 生产厂家名称
     */
    private String prdrName;

    /**
     * 医疗目录编码
     */
    private String medListCodg;

    /**
     * 批准文号
     */
    private String aprvno;

    /**
     * 药品目录审核状态：0-待审核，1-已生效，2-失效，3-驳回
     */
    private String druglistChkStas;

    /**
     * 剂型名称
     */
    private String dosformName;

    /**
     * 删除标识（0未删除，1已删除）
     */
    private String delFlag;

    /**
     * 最小包装单位
     */
    private String minPacunt;

    /**
     * 开始时间
     */
    private Date begntime;

    /**
     * 开始时间集合
     */
    private List<String> begntimeList;

    /**
     * 结束时间
     */
    private Date endtime;

    /**
     * 结束时间集合
     */
    private List<String> endtimeList;

    /**
     * 最小制剂单位
     */
    private String minPrepunt;

    /**
     * 用药途径代码
     */
    private String medcWayCodg;

    /**
     * 用药途径描述
     */
    private String medcWayDscr;

    /**
     * 用药频次编码
     */
    private String medcFrquCodg;

    /**
     * 用药频次名称
     */
    private String medcFrquName;

    /**
     * 病种ID
     */
    private String spdiseId;

    /**
     * 病种编码
     */
    private String spdiseCode;

    /**
     * 病种名称
     */
    private String spdiseCodeName;

    /**
     * 最高限价
     */
    private BigDecimal highLmtpric;

    /**
     * 评估周期(天数)
     */
    private Integer evalPrd;

    /**
     * 双通道标志位
     */
    private String dualchnlFlag;

    /**
     * 门诊统筹标志
     */
    private String oppoolFlag;

    /**
     * 统筹区编号
     */
    private String poolareaNo;

    /**
     * 统筹区名称
     */
    private String poolareaName;

    /**
     * 经办时间
     */
    private Date optTime;

    /**
     * 经办时间集合
     */
    private List<String> optTimeList;

    /**
     * 经办机构编号
     */
    private String optinsNo;

    /**
     * 药品本位码
     */
    private String drugstdcode;

    /**
     * 最小包装数量
     */
    private String minPacCnt;

    /**
     * 注册名称
     */
    private String regName;

    /**
     * 通用目录编码
     */
    private String gennameCodg;

    /**
     * 国家药品编号
     */
    private String natDrugNo;

    /**
     * 医保谈判药品标志
     */
    private String hiNegoDrugFlag;

    /**
     * 医保谈判药品名称
     */
    private String hiNegoDrugName;

    /**
     * 行政区划
     */
    private String admdvs;

    /**
     * 国家医保药品目录甲乙类标识
     */
    private String natHiDruglistChrgitmLv;

    /**
     * 小计量单位销售限价
     */
    private BigDecimal smlUntLmtpric;

    /**
     * 转换比
     */
    private String convrat;

    /**
     * 限制使用范围
     */
    private String lmtUsescp;

    /**
     * 限制使用标志
     */
    private String lmtUsedFlag;

    /**
     * 限制使用说明
     */
    private String natHiDruglistMemo;

    /**
     * 最小使用单位
     */
    private String minUseunt;

    /**
     * 最小销售单位
     */
    private String minSalunt;

    /**
     * 最小计量单位
     */
    private String minUnt;

    /**
     * 最小计价单位
     */
    private String minPrcunt;

    /**
     * 基本药物标志名称
     */
    private String basMednFlagName;

    /**
     * 基本药物标志
     */
    private String basMednFlag;

    /**
     * 限价药品标志
     */
    private String lmtpricDrugFlag;

    /**
     * 特殊药品标志
     */
    private String spDrugFlag;

    /**
     * 是否注射剂药品（0 否 1 是）
     */
    private String injPrepFlag;

    /**
     * 收费项目等级
     */
    private String chrgitmLv;

    /**
     * 医保支付标准
     */
    private BigDecimal hiPayStd;

    /**
     * 创建机构编号
     */
    private String crteOptinsNo;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 创建人ID
     */
    private String crterId;

    /**
     * 创建人姓名
     */
    private String crterName;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 经办人
     */
    private String opterId;

    /**
     * 经办人姓名
     */
    private String opterName;

}



