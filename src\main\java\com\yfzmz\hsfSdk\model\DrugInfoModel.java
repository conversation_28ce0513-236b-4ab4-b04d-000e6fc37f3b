package com.yfzmz.hsfSdk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 购药信息入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrugInfoModel {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 医疗机构订单号
     */
    private String medOrgOrd;

    /**
     * 就诊凭证类型
     */
    private String mdtrtCertType;

    /**
     * 就诊凭证编号
     */
    private String mdtrtCertNo;

    /**
     * 开始时间
     */
    private String begntime;

    /**
     * 医疗费总额
     */
    private BigDecimal medfeeSumamt;

    /**
     * 病种编码
     */
    private String diseCodg;

    /**
     * 病种名称
     */
    private String diseName;

    /**
     * 个人账户使用标志
     */
    private String acctUsedFlag;

    /**
     * 医疗类别
     */
    private String medType;
}
