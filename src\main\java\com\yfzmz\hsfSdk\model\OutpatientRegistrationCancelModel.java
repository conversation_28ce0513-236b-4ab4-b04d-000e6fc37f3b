package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

/**
 * 挂号撤销入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientRegistrationCancelModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 就诊 ID
     */
    private String mdtrtId;

    /**
     * 住院/门诊号
     */
    private String iptOtpNo;
}
