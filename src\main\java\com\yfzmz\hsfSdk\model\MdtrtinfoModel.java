package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 就诊信息入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MdtrtinfoModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 就诊ID
     */
    private String mdtrtId;

    /**
     * 医疗类别
     */
    private String medType;


    /**
     * 开始时间
     */
    private Date begntime;
}
