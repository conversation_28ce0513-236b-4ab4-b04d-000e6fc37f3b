package com.yfzmz.hsfSdk.response.login;

import com.yfzmz.hsfSdk.response.DataHsfResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p> 登录结果信息 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginHsfResponse extends DataHsfResponse<LoginHsfResponse.Login> {

    private static final long serialVersionUID = -230181036026851444L;
    @Data
    public static class Login implements Serializable {
        private static final long serialVersionUID = 5492980549169773957L;
        /**
         * 存储用户认证信息的对象
         * 用于保存用户登录或身份验证后返回的令牌及相关数据
         */
        private TokenResponse token;

        /**
         * 存储用户信息对象
         * 用于保存用户登录或身份验证后返回的用户信息
         */
        private UserInfoResponse userInfo;
    }

}
