package com.yfzmz.hsfSdk.util.sm4;

import cn.hutool.crypto.symmetric.SymmetricCrypto;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SM4加密工具类
 * 提供SM4算法的ECB和CBC模式加密解密功能
 *
 * <AUTHOR>
 * @date 2025-04-02 15:38
 */
public class SM4Utils {

    public SM4Utils() {
    }

    /**
     * 生成SM4密钥
     * 生成16字节（128位）的随机密钥，用于SM4加密算法
     *
     * @return 16字节的随机密钥字符串
     */
    public static String generateKey() {
        SecureRandom random = new SecureRandom();
        byte[] keyBytes = new byte[16]; // SM4密钥长度为16字节
        random.nextBytes(keyBytes);

        // 将字节数组转换为十六进制字符串
        StringBuilder keyBuilder = new StringBuilder();
        for (byte b : keyBytes) {
            keyBuilder.append(String.format("%02x", b & 0xff));
        }

        return keyBuilder.toString().substring(0, 16); // 确保返回16字符长度
    }

    /**
     * 生成SM4密钥Base64
     * 生成32字节（128位）的随机密钥，用于SM4加密算法
     *
     * @return 16字节的随机密钥字符串
     */
    public static String generateKeyBase64() {
        SecureRandom random = new SecureRandom();
        byte[] keyBytes = new byte[32]; // SM4密钥长度为16字节
        random.nextBytes(keyBytes);

        // 将字节数组转换为十六进制字符串
        StringBuilder keyBuilder = new StringBuilder();
        for (byte b : keyBytes) {
            keyBuilder.append(String.format("%02x", b & 0xff));
        }

        return Base64.getEncoder().encodeToString(keyBuilder.toString().substring(0, 32).getBytes(StandardCharsets.UTF_8)); // 确保返回16字符长度
    }

    /**
     * 生成SM4初始化向量(IV)
     * 生成16字节的随机初始化向量，用于CBC模式加密
     *
     * @return 16字节的随机IV字符串
     */
    public static String generateIV() {
        SecureRandom random = new SecureRandom();
        byte[] ivBytes = new byte[16]; // SM4 IV长度为16字节
        random.nextBytes(ivBytes);

        // 将字节数组转换为十六进制字符串
        StringBuilder ivBuilder = new StringBuilder();
        for (byte b : ivBytes) {
            ivBuilder.append(String.format("%02x", b & 0xff));
        }

        return ivBuilder.toString().substring(0, 16); // 确保返回16字符长度
    }

    /**
     * SM4 ECB模式加密
     * 使用ECB模式对明文进行SM4加密，返回Base64编码的密文
     *
     * @param plainText 待加密的明文字符串
     * @param secretKey 加密密钥，如果长度超过16字节会被截取前16字节
     * @return Base64编码的密文字符串，加密失败返回null
     */
    public static String encryptDataECB(String plainText, String secretKey) {
        try {
            // 确保密钥长度不超过16字节
            if (secretKey.length() > 16) {
                secretKey = secretKey.substring(0, 16);
            }

            // 创建SM4对称加密对象，使用ECB模式和PKCS5填充
            SymmetricCrypto symmetricCrypto = new SymmetricCrypto("SM4/ECB/PKCS5Padding", secretKey.getBytes(StandardCharsets.UTF_8));
            // 加密并进行Base64编码
            String cipherText = (new BASE64Encoder()).encode(symmetricCrypto.encrypt(plainText));

            // 清理Base64字符串中的空白字符
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }

            return cipherText;
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }

    /**
     * SM4 ECB模式解密
     * 使用ECB模式对Base64编码的密文进行SM4解密
     *
     * @param cipherText Base64编码的密文字符串
     * @param secretKey 解密密钥，如果长度超过16字节会被截取前16字节
     * @return 解密后的明文字符串，解密失败返回null
     */
    public static String decryptDataECB(String cipherText, String secretKey) {
        try {
            // 确保密钥长度不超过16字节
            if (secretKey.length() > 16) {
                secretKey = secretKey.substring(0, 16);
            }

            // 创建SM4对称解密对象，使用ECB模式和PKCS5填充
            SymmetricCrypto symmetricCrypto = new SymmetricCrypto("SM4/ECB/PKCS5Padding", secretKey.getBytes(StandardCharsets.UTF_8));
            // 解密Base64编码的密文
            String s = symmetricCrypto.decryptStr(cipherText);

            // 处理URL编码问题
            s = s.replaceAll("%(?![0-9a-fA-F]{2})", "%25"); // 修复不完整的URL编码
            s = s.replaceAll("\\+", "%2B"); // 处理加号编码问题

            // URL解码并返回最终明文
            return URLDecoder.decode(s, StandardCharsets.UTF_8.toString());
        } catch (Exception var4) {
            var4.printStackTrace();
            return null;
        }
    }

    /**
     * SM4 CBC模式加密
     * 使用CBC模式对明文进行SM4加密，返回Base64编码的密文
     *
     * @param plainText 待加密的明文字符串
     * @param secretKey 加密密钥，如果长度超过16字节会被截取前16字节
     * @param iv 初始化向量，用于CBC模式加密
     * @return Base64编码的密文字符串，加密失败返回null
     */
    public static String encryptDataCBC(String plainText, String secretKey, String iv) {
        try {
            // 对明文进行URL编码预处理
            plainText = URLEncoder.encode(plainText, StandardCharsets.UTF_8.toString());

            // 初始化SM4上下文
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true; // 启用填充
            ctx.mode = 1; // 设置为加密模式

            // 确保密钥长度不超过16字节
            if (secretKey.length() > 16) {
                secretKey = secretKey.substring(0, 16);
            }

            // 准备密钥和初始化向量的字节数组
            byte[] keyBytes = secretKey.getBytes();
            byte[] ivBytes = iv.getBytes();

            // 创建SM4实例并设置加密密钥
            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);

            // 执行CBC模式加密
            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes(StandardCharsets.UTF_8));

            // 将加密结果进行Base64编码
            String cipherText = (new BASE64Encoder()).encode(encrypted);

            // 清理Base64字符串中的空白字符
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }

            return cipherText;
        } catch (Exception var11) {
            var11.printStackTrace();
            return null;
        }
    }

    /**
     * SM4 CBC模式解密
     * 使用CBC模式对Base64编码的密文进行SM4解密
     *
     * @param cipherText Base64编码的密文字符串
     * @param secretKey 解密密钥，如果长度超过16字节会被截取前16字节
     * @param iv 初始化向量，必须与加密时使用的IV相同
     * @return 解密后的明文字符串，解密失败返回null
     */
    public static String decryptDataCBC(String cipherText, String secretKey, String iv) {
        try {
            // 初始化SM4上下文
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true; // 启用填充
            ctx.mode = 0; // 设置为解密模式

            // 确保密钥长度不超过16字节
            if (secretKey.length() > 16) {
                secretKey = secretKey.substring(0, 16);
            }

            // 准备密钥和初始化向量的字节数组
            byte[] keyBytes = secretKey.getBytes();
            byte[] ivBytes = iv.getBytes();

            // 创建SM4实例并设置解密密钥
            SM4 sm4 = new SM4();
            sm4.sm4_setkey_dec(ctx, keyBytes);

            // 解码Base64密文并执行CBC模式解密
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, (new BASE64Decoder()).decodeBuffer(cipherText));

            // 将解密结果转换为UTF-8字符串并返回
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception var8) {
            var8.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(generateKeyBase64());
        System.out.println(generateKeyBase64().length());
    }


}
