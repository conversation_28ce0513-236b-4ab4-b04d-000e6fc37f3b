package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 药店药品销售入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSellModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 医疗目录编码
     */
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    private String fixmedinsHilistId;

    /**
     * 定点医药机构目录名称
     */
    private String fixmedinsHilistName;

    /**
     * 定点医药机构批次流水号
     */
    private String fixmedinsBchno;

    /**
     * 开方医师证件类型
     */
    private String prscDrCertType;

    /**
     * 开方医师证件号码
     */
    private String prscDrCertno;

    /**
     * 开方医师姓名
     */
    private String prscDrName;

    /**
     * 药师证件类型
     */
    private String pharCertType;

    /**
     * 药师证件号码
     */
    private String pharCertno;

    /**
     * 药师姓名
     */
    private String pharName;

    /**
     * 药师执业资格证号
     */
    private String pharPracCertNo;

    /**
     * 医保费用结算类型
     */
    private String hiFeesetlType;


    /**
     * 就医流水号
     */
    private String mdtrtSn;

    /**
     * 人员证件类型
     */
    private String psnCertType;

    /**
     * 生产批号
     */
    private String manuLotnum;

    /**
     * 生产日期
     */
    private Date manuDate;

    /**
     * 有效期止
     */
    private Date expyEnd;

    /**
     * 处方药标志
     */
    private String rxFlag;

    /**
     * 拆零标志
     */
    private String trdnFag;

    /**
     * 最终成交单价
     */
    private BigDecimal finlTrnsPric;

    /**
     * 外购处方标志
     */
    private String rxCircFlag;

    /**
     * 零售单据号
     */
    private String rtalDocno;

    /**
     * 销售出库单据号
     */
    private String stooutNo;

    /**
     * 批次号
     */
    private String bchno;

    /**
     * 销售/退货数量
     */
    private String selRetnCnt;

    /**
     * 销售/退货时间
     */
    private Date selRetnTime;

    /**
     * 销售/退货经办人姓名
     */
    private String selRetnOpterName;


    /**
     * 就诊结算类型
     */
    private String mdtrtSetlType;
}
