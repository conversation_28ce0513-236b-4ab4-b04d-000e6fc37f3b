package com.yfzmz.hsfSdk;

/**
 * <p> 易复诊请求客户端 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 14:45
 **/
public interface YfzmzHsfClient {

    /**
     * 执行易复诊铭智请求的通用方法
     * 该方法是一个泛型方法，允许传入任何继承了YfzmzResponse类型的响应对象
     * 主要用于简化请求的执行过程，将请求的发送和响应的处理封装在一起
     *
     * @param request 易复诊铭智请求对象，包含了请求的所有参数和信息
     * @param <T>     泛型参数，代表返回的响应对象类型，必须是YfzmzResponse的子类
     * @return 返回处理后的响应对象，类型为泛型参数T指定的类型
     */
    <T extends YfzmzHsfResponse> T execute(YfzmzHsfRequest<T> request);

    /**
     * 使用指定的token执行易复诊铭智请求的通用方法
     * 该方法是一个泛型方法，允许传入任何继承了YfzmzResponse类型的响应对象
     * 主要用于简化请求的执行过程，特别是当需要使用特定的认证令牌时
     *
     * @param request 易复诊铭智请求对象，包含了请求的所有参数和信息
     * @param token   字符串类型的认证令牌，用于请求的认证
     * @param <T>     泛型参数，代表返回的响应对象类型，必须是YfzmzResponse的子类
     * @return 返回处理后的响应对象，类型为泛型参数T指定的类型
     */
    <T extends YfzmzHsfResponse> T execute(YfzmzHsfRequest<T> request, String token);

}