package com.yfzmz.hsfSdk.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 审方药师信息管理表DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PharInfoMgtBModel {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 药师ID
     */
    private String pharInfoId;

    /**
     * 药师ID集合
     */
    private List<String> pharInfoIds;

    /**
     * 药师代码
     */
    private String pharCode;

    /**
     * 药师名字
     */
    private String pharName;

    /**
     * 药师类别 (1-执业药师,2-从业药师3-卫生职称药师)
     */
    private String pharType;

    /**
     * 药师类别名称
     */
    private String pharTypeName;

    /**
     * 执业机构编号
     */
    private String pracinsNo;

    /**
     * 执业机构名称
     */
    private String pracinsName;

    /**
     * 执业地区
     */
    private String pracRegn;

    /**
     * 执业地区名称
     */
    private String pracRegnName;

    /**
     * 药师执业类别(1-药学,2-中药学,3-药学与中药学,4其他)
     */
    private String pharPracType;

    /**
     * 药师执业类别名称
     */
    private String pharPracTypeName;

    /**
     * 药师执业范围(1-经营批发,2-经营零售3-生产4-使用(卫生职称药师))
     */
    private String pharPracScp;

    /**
     * 药师执业范围名称
     */
    private String pharPracScpName;

    /**
     * 开通状态编码(0-待审核，1-已生效，2-已失效，3-驳回)
     */
    private String openStasCodg;

    /**
     * 开通状态名称
     */
    private String openStasName;

    /**
     * 离职时间
     */
    private Date nempTime;

    /**
     * 离职时间集合
     */
    private List<String> nempTimeList;

    /**
     * 个人能力简介
     */
    private String psnItro;

    /**
     * 删除标识（0未删除，1已删除）
     */
    private String delFlag;

    /**
     * 统筹区编号
     */
    private String poolareaNo;

    /**
     * 统筹区名称
     */
    private String poolareaName;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 认证编码
     */
    private String crtfCode;

    /**
     * 执业状态：1正常，2暂停，3终止
     */
    private String pracStas;

    /**
     * 执业状态名称
     */
    private String pracStasName;

    /**
     * 门诊统筹标志
     */
    private String oppoolFlag;

    /**
     * 人员编号
     */
    private String psnNo;

    /**
     * 有效标志位
     */
    private String valiFlag;

    /**
     * 经办时间
     */
    private Date optTime;

    /**
     * 经办时间集合
     */
    private List<String> optTimeList;

    /**
     * 经办机构编号
     */
    private String optinsNo;

    /**
     * 创建机构编号
     */
    private String crteOptinsNo;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 创建人ID
     */
    private String crterId;

    /**
     * 创建人姓名
     */
    private String crterName;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 经办人
     */
    private String optId;

    /**
     * 经办人姓名
     */
    private String opterName;

}



