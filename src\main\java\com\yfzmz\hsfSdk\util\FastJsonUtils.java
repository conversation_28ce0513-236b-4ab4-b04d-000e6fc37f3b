package com.yfzmz.hsfSdk.util;

import com.alibaba.fastjson.JSONObject;

/**
 * <p> Json工具类 </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-25 16:18
 **/
public class FastJsonUtils {
    /**
     * model 转 JSON
     * @param t 实体类
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> String modelToJson(T t) {
        return JSONObject.toJSONString(t);
    }

    /**
     * json转model对象
     *
     * @param s   json字符串
     * @param tclass 实体类
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> T jsonToModel(String s, Class<T> tclass) {
        return JSONObject.parseObject(s, tclass);
    }

    /**
     * Object数据转换为model实体类
     * @param object data数据
     * @param tclass 实体类
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> T objectToModel(Object object, Class<T> tclass) {
        return jsonToModel(modelToJson(object), tclass);
    }

    /**
     * 字符串转JSONObject
     * @param s 字符串
     * @return 结果
     */
    public static JSONObject stringToJsonObject(String s) {
        return JSONObject.parseObject(s);
    }

    /**
     * bean对象转换为json对象
     * @param data bean对象
     * @return 结果
     */
    public static JSONObject objectToJsonObject(Object data) {
        return stringToJsonObject(modelToJson(data));
    }

}
