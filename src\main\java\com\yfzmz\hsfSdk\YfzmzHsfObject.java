package com.yfzmz.hsfSdk;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>  </p>
 *
 * @program: yfzmz-sdk-java
 * @author: 许仕昊
 * @create: 2025-06-24 15:11
 **/
@Data
public abstract class YfzmzHsfObject implements Serializable {
    private static final long serialVersionUID = 773632493323990010L;

    /**
     * 医疗机构编号
     */
    private String fixmedinsCode;

    /**
     * 人员编号
     */
    private String psnNo;

    /**
     * 医疗机构名称
     */
    private String fixmedinsName;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 人员姓名
     */
    private String psnName;

    /**
     * 参保地医保区划
     */
    private String insuplcAdmdvs;

    /**
     * 排序集合
     */
    private String sortArrayJson;

    /**
     * 起始页
     */
    private int currentPage;

    /**
     * 条数
     */
    private int pageSize;
}
