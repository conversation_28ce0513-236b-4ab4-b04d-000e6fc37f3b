package com.yfzmz.hsfSdk.model;

import com.yfzmz.hsfSdk.YfzmzHsfObject;
import lombok.*;

import java.util.List;

/**
 * 就诊信息上传入参
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutpatientMdtrtinfoUpModel extends YfzmzHsfObject {

    private static final long serialVersionUID = 2239184773176755963L;

    /**
     * 就诊信息
     */
    private MdtrtinfoModel mdtrtinfo;

    /**
     * 诊断信息
     */
    private List<DiseinfoModel> diseinfo;
}
