package com.yfzmz.hsfSdk.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 查询诊断数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14 14:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CdsRxDiagSttEModel {

    private static final long serialVersionUID = 2239184773176755963L;
/**
     * 数据唯一记录号
     */
    private String rid;

    /**
     * 数据唯一记录号集合
     */
    private List<String> rids;

    /**
     * 处方诊断信息统计ID
     */
    private String rxDiagSttId;

    /**
     * 医保区划
     */
    private String admdvs;

    /**
     * 医保区划名称
     */
    private String admdvsName;

    /**
     * 统计日期
     */
    private String sttDate;

    /**
     * 诊断编码
     */
    private String diagCode;

    /**
     * 诊断名称
     */
    private String diagName;

    /**
     * 处方开方数量
     */
    private Integer rxPrscCnt;

    /**
     * 处方开方金额
     */
    private BigDecimal rxPrscAmt;

    /**
     * 处方开方人数
     */
    private Integer rxPrscPsnCnt;

    /**
     * 处方结算人数
     */
    private Integer rxSetlPsnCnt;

    /**
     * 处方结算数量
     */
    private Integer rxSetlCnt;

    /**
     * 处方结算金额
     */
    private BigDecimal rxSetlAmt;

    /**
     * 处方统筹基金支付金额
     */
    private BigDecimal rxHifiPay;

    /**
     * 处方个人账户支付金额
     */
    private BigDecimal rxAcctPay;

    /**
     * 处方现金支付金额
     */
    private BigDecimal rxCashpay;

    /**
     * 统筹区编号
     */
    private String poolareaNo;

    /**
     * 统筹区名称
     */
    private String poolareaName;

    /**
     * 数据创建时间
     */
    private Date crteTime;

    /**
     * 数据创建时间集合
     */
    private List<String> crteTimeList;

    /**
     * 数据更新时间
     */
    private Date updtTime;

    /**
     * 数据更新时间集合
     */
    private List<String> updtTimeList;

    /**
     * 开始时间
     */
    private String sttDateStart;

    /**
     * 结束时间
     */
    private String sttDateEnd;

}



